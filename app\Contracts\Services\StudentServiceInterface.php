<?php

namespace App\Contracts\Services;

use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;

interface StudentServiceInterface
{
    /**
     * Get all students.
     */
    public function getAllStudents(array $filters = []): Collection;

    /**
     * Get all active students.
     */
    public function getAllActiveStudents(): Collection;

    /**
     * Get a student by ID.
     */
    public function getStudentById(int $id): Student;

    /**
     * Create a new student with user account.
     *
     * @throws \Exception
     */
    public function createStudent(array $data): Student;

    /**
     * Update student data.
     *
     * @throws \Exception
     */
    public function updateStudent(int $id, array $data): bool;

    /**
     * Enroll student to a classroom for a specific academic year.
     *
     * @throws \Exception
     */
    public function enrollStudentToClassroom(int $studentId, int $classroomId, int $academicYearId): bool;

    /**
     * Get student's classrooms.
     */
    public function getStudentClassrooms(int $studentId): Collection;

    /**
     * Get student's performance data.
     */
    public function getStudentPerformance(int $studentId): array;

    /**
     * Get total number of students.
     */
    public function getTotalStudents(): int;
}
