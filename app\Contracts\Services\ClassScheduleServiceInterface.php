<?php

namespace App\Contracts\Services;

use App\Models\ClassSchedule;
use Illuminate\Database\Eloquent\Collection;

interface ClassScheduleServiceInterface
{
    /**
     * Get all class schedules
     */
    public function getAllClassSchedules(): Collection;

    /**
     * Get a class schedule by ID
     *
     * @param  mixed  $id
     */
    public function getClassScheduleById(int $id): ClassSchedule;

    /**
     * Create a new class schedule
     */
    public function createClassSchedule(array $data): ClassSchedule;

    /**
     * Update a class schedule
     *
     * @param  mixed  $id
     */
    public function updateClassSchedule($id, array $data): bool;

    /**
     * Delete a class schedule
     *
     * @param  mixed  $id
     */
    public function deleteClassSchedule($id): bool;

    /**
     * Get schedules by classroom and academic year
     */
    public function getSchedulesByClassroomAndAcademicYear(int $classroomId, int $academicYearId): Collection;

    /**
     * Find existing schedule
     */
    public function findExistingSchedule(int $lessonHourId, string $dayOfWeek, ?int $excludeId = null): ?ClassSchedule;

    /**
     * Check for teacher schedule conflict
     */
    public function checkTeacherScheduleConflict(int $teacherId, int $lessonHourId, string $dayOfWeek, ?int $excludeId = null): bool;
}
