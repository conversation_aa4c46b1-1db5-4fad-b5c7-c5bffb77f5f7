<?php

namespace App\Contracts\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

interface BaseRepositoryInterface
{
    /**
     * Find a model by its primary key.
     */
    public function findById(int $id): Model;

    /**
     * Get all models with optional filters.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Create a new model with the given data.
     */
    public function create(array $data): Model;

    /**
     * Update a model with the given data.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a model by its primary key.
     */
    public function delete(int $id): bool;
}
