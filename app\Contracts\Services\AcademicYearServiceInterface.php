<?php

namespace App\Contracts\Services;

use App\Models\AcademicYear;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface AcademicYearServiceInterface
{
    /**
     * Get all academic years.
     *
     * @return Collection
     */
    public function getAllAcademicYears();

    /**
     * Get all academic years with pagination.
     *
     * @return LengthAwarePaginator
     */
    public function getAllAcademicYearsPaginated(int $perPage = 10);

    /**
     * Get an academic year by ID.
     *
     * @return AcademicYear
     */
    public function getAcademicYearById(int $id);

    /**
     * Create a new academic year.
     *
     * @return AcademicYear
     */
    public function createAcademicYear(array $data);

    /**
     * Update an existing academic year.
     *
     * @return AcademicYear
     */
    public function updateAcademicYear(int $id, array $data);

    /**
     * Change the status of an academic year.
     *
     * @return AcademicYear
     */
    public function changeAcademicYearStatus(int $id, string $status);

    /**
     * Get current active academic year.
     *
     * @return AcademicYear|null
     */
    public function getCurrentAcademicYear();

    /**
     * Get all active academic years.
     *
     * @return Collection
     */
    public function getActiveAcademicYears();

    /**
     * Get all active academic years (alias for consistency with other services).
     *
     * @return Collection
     */
    public function getAllActiveAcademicYears();

    /**
     * Get active academic year.
     */
    public function getActiveAcademicYear(): ?AcademicYear;

    /**
     * Check if academic year has related data.
     */
    public function hasRelatedData(int $id): bool;

    /**
     * Get a list of all academic years for API.
     *
     * @return Collection
     */
    public function getAcademicYearsList();

    /**
     * Get academic year with its relations.
     *
     * @return AcademicYear
     */
    public function getAcademicYearWithRelations(int $id);

    /**
     * Delete an academic year.
     */
    public function deleteAcademicYear(AcademicYear $academicYear): bool;
}
