<?php

namespace App\Contracts\Services;

use App\Models\Classroom;
use Illuminate\Database\Eloquent\Collection;

interface ClassroomServiceInterface
{
    /**
     * Get all classrooms.
     *
     * @return Collection
     */
    public function getAllClassrooms();

    /**
     * Get all active classrooms.
     *
     * @return Collection
     */
    public function getAllActiveClassrooms();

    /**
     * Get classrooms by academic year.
     *
     * @return Collection
     */
    public function getClassroomsByAcademicYear(int $academicYearId);

    /**
     * Get a classroom by ID.
     *
     * @return Classroom
     */
    public function getClassroomById(int $id);

    /**
     * Create a new classroom.
     *
     * @return Classroom
     */
    public function createClassroom(array $data);

    /**
     * Update a classroom.
     *
     * @return bool
     */
    public function updateClassroom(int $id, array $data);

    /**
     * Change the teacher assigned to a classroom.
     *
     * @return bool
     */
    public function changeClassroomTeacher(int $classroomId, int $teacherId);

    /**
     * Get students in a classroom.
     *
     * @return Collection
     */
    public function getStudentsInClassroom(int $classroomId);

    /**
     * Get classroom schedule.
     *
     * @return Collection
     */
    public function getClassroomSchedule(int $classroomId);

    /**
     * Remove a student from a classroom.
     *
     * @return bool
     */
    public function removeStudentFromClassroom(int $classroomId, int $studentId);

    /**
     * Get active classrooms by academic year.
     *
     * @return Collection
     */
    public function getActiveClassroomsByAcademicYear(int $academicYearId);

    /**
     * Get available students for a classroom (not enrolled in any class for current academic year).
     *
     * @return Collection
     */
    public function getAvailableStudents(int $academicYearId);

    /**
     * Get count of active classrooms.
     */
    public function getActiveClassroomsCount(): int;

    /**
     * Enroll multiple students to a classroom.
     */
    public function enrollStudentsToClassroom(int $classroomId, array $studentIds): bool;

    /**
     * Check if a student is already enrolled in any classroom for a specific academic year.
     */
    public function isStudentEnrolledInAcademicYear(int $studentId, int $academicYearId): bool;

    /**
     * Delete a classroom.
     */
    public function deleteClassroom(int $id): bool;
}
