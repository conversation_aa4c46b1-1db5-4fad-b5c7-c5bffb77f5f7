<?php

namespace App\Services;

use App\Contracts\Interfaces\AttendanceRepositoryInterface;
use App\Contracts\Services\AttendanceServiceInterface;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\Attendance;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Throwable;

final class AttendanceService implements AttendanceServiceInterface
{
    private AttendanceRepositoryInterface $attendanceRepository;

    /**
     * AttendanceService constructor.
     */
    public function __construct(AttendanceRepositoryInterface $attendanceRepository)
    {
        $this->attendanceRepository = $attendanceRepository;
    }

    /**
     * Get all attendances with optional filtering
     *
     * @return Collection|LengthAwarePaginator
     */
    public function getAllAttendances(array $filters = [])
    {
        $relations = ['classSchedule.classroom', 'classSchedule.subject', 'student.user', 'teacher.user'];

        return $this->attendanceRepository->getAllWithRelations($relations, $filters);
    }

    /**
     * Get attendance by ID
     *
     * @throws NotFoundException
     */
    public function getAttendanceById(int $id): Attendance
    {
        return $this->attendanceRepository->findById($id);
    }

    /**
     * Record a new attendance
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function recordAttendance(array $data): Attendance
    {
        try {
            return $this->attendanceRepository->recordAttendance($data);
        } catch (BusinessLogicException | DatabaseException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal mencatat kehadiran: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Update an existing attendance
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function updateAttendance(int $id, array $data): bool
    {
        try {
            return $this->attendanceRepository->updateAttendance($id, $data);
        } catch (BusinessLogicException | DatabaseException | NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal memperbarui kehadiran: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Delete an attendance
     *
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function deleteAttendance(int $id): bool
    {
        try {
            return $this->attendanceRepository->delete($id);
        } catch (NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal menghapus kehadiran: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get class attendance for a specific date
     */
    public function getClassAttendance(int $classScheduleId, string $date): Collection
    {
        try {
            return $this->attendanceRepository->getClassAttendance($classScheduleId, $date);
        } catch (Throwable $e) {
            // Convert any exceptions to DatabaseException
            throw new DatabaseException('Gagal mendapatkan data kehadiran kelas: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get student attendance with filters
     */
    public function getStudentAttendance(int $studentId, array $filters = []): Collection
    {
        try {
            return $this->attendanceRepository->getStudentAttendance($studentId, $filters);
        } catch (Throwable $e) {
            // Convert any exceptions to DatabaseException
            throw new DatabaseException('Gagal mendapatkan data kehadiran siswa: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get teacher attendance with filters
     */
    public function getTeacherAttendance(int $teacherId, array $filters = []): Collection
    {
        try {
            return $this->attendanceRepository->getTeacherAttendance($teacherId, $filters);
        } catch (Throwable $e) {
            // Convert any exceptions to DatabaseException
            throw new DatabaseException('Gagal mendapatkan data kehadiran guru: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get attendance summary for a classroom in a specific month
     *
     * @param  string  $month  Format: YYYY-MM
     */
    public function getAttendanceSummary(int $classroomId, string $month): array
    {
        try {
            return $this->attendanceRepository->getAttendanceSummary($classroomId, $month);
        } catch (Throwable $e) {
            // Convert any exceptions to DatabaseException
            throw new DatabaseException('Gagal mendapatkan ringkasan kehadiran: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }
}
