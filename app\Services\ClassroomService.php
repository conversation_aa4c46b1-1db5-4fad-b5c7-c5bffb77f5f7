<?php

namespace App\Services;

use App\Contracts\Interfaces\ClassroomRepositoryInterface;
use App\Contracts\Services\ClassroomServiceInterface;
use App\Models\Classroom;
use Illuminate\Database\Eloquent\Collection;

class ClassroomService implements ClassroomServiceInterface
{
    protected $classroomRepository;

    /**
     * ClassroomService constructor.
     */
    public function __construct(ClassroomRepositoryInterface $classroomRepository)
    {
        $this->classroomRepository = $classroomRepository;
    }

    /**
     * Get all classrooms with optional filtering.
     *
     * @param  array  $filters  Optional filters
     * @return Collection
     */
    public function getAllClassrooms(array $filters = [])
    {
        return $this->classroomRepository->getAll($filters);
    }

    /**
     * Get all active classrooms.
     *
     * @return Collection
     */
    public function getAllActiveClassrooms()
    {
        return $this->classroomRepository->getActiveClassrooms();
    }

    /**
     * Get classrooms by academic year.
     *
     * @return Collection
     */
    public function getClassroomsByAcademicYear(int $academicYearId)
    {
        return $this->classroomRepository->getByAcademicYear($academicYearId);
    }

    /**
     * Get a classroom by ID.
     *
     * @return Classroom
     */
    public function getClassroomById(int $id)
    {
        return $this->classroomRepository->findById($id);
    }

    /**
     * Create a new classroom.
     *
     * @return Classroom
     */
    public function createClassroom(array $data)
    {
        return $this->classroomRepository->create($data);
    }

    /**
     * Update a classroom.
     *
     * @return bool
     */
    public function updateClassroom(int $id, array $data)
    {
        return $this->classroomRepository->update($id, $data);
    }

    /**
     * Change the teacher assigned to a classroom.
     *
     * @return bool
     */
    public function changeClassroomTeacher(int $classroomId, int $teacherId)
    {
        return $this->classroomRepository->changeTeacher($classroomId, $teacherId);
    }

    /**
     * Get students in a classroom.
     *
     * @return Collection
     */
    public function getStudentsInClassroom(int $classroomId)
    {
        return $this->classroomRepository->getClassroomStudents($classroomId);
    }

    /**
     * Get classroom schedule.
     *
     * @return Collection
     */
    public function getClassroomSchedule(int $classroomId)
    {
        return $this->classroomRepository->getClassroomSchedule($classroomId);
    }

    /**
     * Remove a student from a classroom.
     */
    public function removeStudentFromClassroom(int $classroomId, int $studentId): bool
    {
        return $this->classroomRepository->removeStudentFromClassroom($classroomId, $studentId);
    }

    /**
     * Get active classrooms by academic year.
     *
     * @return Collection
     */
    public function getActiveClassroomsByAcademicYear(int $academicYearId)
    {
        return $this->classroomRepository->getActiveByAcademicYear($academicYearId);
    }

    /**
     * Get available students for a classroom (not enrolled in any class for current academic year).
     *
     * @return Collection
     */
    public function getAvailableStudents(int $academicYearId)
    {
        return $this->classroomRepository->getAvailableStudents($academicYearId);
    }

    /**
     * Get count of active classrooms.
     *
     * @return int The number of active classrooms
     */
    public function getActiveClassroomsCount(): int
    {
        return $this->classroomRepository->getActiveCount();
    }

    /**
     * Enroll multiple students to a classroom.
     */
    public function enrollStudentsToClassroom(int $classroomId, array $studentIds): bool
    {
        return $this->classroomRepository->enrollStudentsToClassroom($classroomId, $studentIds);
    }

    /**
     * Check if a student is already enrolled in any classroom for a specific academic year.
     */
    public function isStudentEnrolledInAcademicYear(int $studentId, int $academicYearId): bool
    {
        return $this->classroomRepository->isStudentEnrolledInAcademicYear($studentId, $academicYearId);
    }

    /**
     * Delete a classroom.
     */
    public function deleteClassroom(int $id): bool
    {
        return $this->classroomRepository->deleteClassroom($id);
    }

    /**
     * Get classrooms by shift.
     */
    public function getClassroomsByShift(int $shiftId): Collection
    {
        return Classroom::where('shift_id', $shiftId)
            ->with(['program', 'teacher.user', 'academicYear'])
            ->get();
    }
}
