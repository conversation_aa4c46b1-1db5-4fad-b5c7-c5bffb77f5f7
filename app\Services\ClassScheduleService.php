<?php

namespace App\Services;

use App\Contracts\Interfaces\ClassScheduleRepositoryInterface;
use App\Contracts\Services\ClassScheduleServiceInterface;
use App\Exceptions\ClassScheduleException;
use App\Models\ClassSchedule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ClassScheduleService implements ClassScheduleServiceInterface
{
    /**
     * Class schedule repository instance
     *
     * @var ClassScheduleRepositoryInterface
     */
    public function __construct(
        private readonly ClassScheduleRepositoryInterface $classScheduleRepository
    ) {
    }

    /**
     * Get all class schedules
     */
    public function getAllClassSchedules(): Collection
    {
        return $this->classScheduleRepository->getAllClassSchedules();
    }

    /**
     * Get a class schedule by ID
     *
     * @param  mixed  $id
     */
    public function getClassScheduleById(int $id): ClassSchedule
    {
        return $this->classScheduleRepository->getClassScheduleById($id);
    }

    /**
     * Create a new class schedule
     */
    public function createClassSchedule(array $data): ClassSchedule
    {
        return DB::transaction(function () use ($data) {
            // Business validation
            $this->validateScheduleData($data);
            
            // Check for conflicts
            $this->validateScheduleConflicts($data);
            
            return $this->classScheduleRepository->createClassSchedule($data);
        });
    }

    /**
     * Update a class schedule
     *
     * @param  mixed  $id
     */
    public function updateClassSchedule($id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            $classSchedule = ClassSchedule::findOrFail($id);

            // Check for slot conflicts
            $existingSchedule = ClassSchedule::where('lesson_hour_id', $data['lesson_hour_id'])
                ->where('day_of_week', $data['day_of_week'])
                ->whereHas('teacherAssignment', function ($query) use ($data) {
                    $query->where('classroom_id', $data['classroom_id'])
                          ->where('academic_year_id', $data['academic_year_id']);
                })
                ->where('id', '!=', $id)
                ->first();

            if ($existingSchedule) {
                throw new ClassScheduleException('Slot jadwal sudah terisi.', 422);
            }

            // Check teacher availability
            $teacherAssignment = app(TeacherAssignmentService::class)->getTeacherAssignmentById($data['teacher_assignment_id']);
            $conflictingSchedule = ClassSchedule::where('teacher_assignment_id', $data['teacher_assignment_id'])
                ->where('lesson_hour_id', $data['lesson_hour_id'])
                ->where('day_of_week', $data['day_of_week'])
                ->where('id', '!=', $id)
                ->exists();
            
            if ($conflictingSchedule) {
                throw new ClassScheduleException('Guru sudah memiliki jadwal pada slot ini.', 422);
            }

            // Update the schedule
            $updated = $classSchedule->update([
                'teacher_assignment_id' => $data['teacher_assignment_id'],
                'lesson_hour_id' => $data['lesson_hour_id'],
                'day_of_week' => $data['day_of_week'],
                'status' => $data['status'] ?? $classSchedule->status,
            ]);

            return $updated;
        });
    }

    /**
     * Delete a class schedule
     *
     * @param  mixed  $id
     */
    public function deleteClassSchedule($id): bool
    {
        return $this->classScheduleRepository->deleteClassSchedule($id);
    }

    /**
     * Get schedules by classroom and academic year
     *
     * @return array
     */
    public function getSchedulesByClassroomAndAcademicYear(int $classroomId, int $academicYearId): Collection
    {
        return $this->classScheduleRepository->getSchedulesByClassroomAndAcademicYear($classroomId, $academicYearId);
    }

    /**
     * Find existing schedule
     */
    public function findExistingSchedule(int $lessonHourId, string $dayOfWeek, ?int $excludeId = null): ?ClassSchedule
    {
        return $this->classScheduleRepository->findExistingSchedule($lessonHourId, $dayOfWeek, $excludeId);
    }

    /**
     * Check for teacher schedule conflict
     */
    public function checkTeacherScheduleConflict(int $teacherId, int $lessonHourId, string $dayOfWeek, ?int $excludeId = null): bool
    {
        return $this->classScheduleRepository->checkTeacherScheduleConflict($teacherId, $lessonHourId, $dayOfWeek, $excludeId);
    }

    /**
     * Get formatted schedules for a classroom and academic year
     */
    public function getFormattedSchedules(int $classroomId, int $academicYearId): array
    {
        $rawSchedules = $this->getSchedulesByClassroomAndAcademicYear($classroomId, $academicYearId);
        $lessonHours = app(LessonHourService::class)->getLessonHoursByClassroomId($classroomId);

        $schedules = [];

        // Initialize the schedules array with lesson hours
        foreach ($lessonHours as $hour) {
            $schedules[$hour->id] = [
                'hour' => $hour,
                'days' => array_fill_keys(['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], null),
            ];
        }

        // Fill in the schedules with the actual data
        foreach ($rawSchedules as $schedule) {
            if (isset($schedules[$schedule->lesson_hour_id])) {
                $schedules[$schedule->lesson_hour_id]['days'][$schedule->day_of_week] = $schedule;
            }
        }

        return $schedules;
    }

    /**
     * Get complete schedule data for API/AJAX responses
     */
    public function getScheduleData(int $classroomId, int $academicYearId): array
    {
        // Validate input parameters
        if (!$classroomId || !$academicYearId) {
            throw new \InvalidArgumentException('Classroom ID and Academic Year ID are required');
        }

        try {
            // Get classroom with shift relationship
            $classroom = app(ClassroomService::class)->getClassroomById($classroomId);
            $classroom->load('shift');
            
            // Get academic year
            $academicYear = app(AcademicYearService::class)->getAcademicYearById($academicYearId);

            // Get lesson hours for this classroom
            $lessonHours = app(LessonHourService::class)->getLessonHoursByClassroomId($classroomId);

            // Format lesson hours time display
            $lessonHours->each(function ($hour) {
                $hour->formatted_start_time = date('H:i', strtotime($hour->start_time));
                $hour->formatted_end_time = date('H:i', strtotime($hour->end_time));
            });

            // Get teacher assignments for this classroom and academic year
            $teacherAssignments = app(TeacherAssignmentService::class)->getAllTeacherAssignments([
                'classroom_id' => $classroomId,
                'academic_year_id' => $academicYearId,
            ]);

            // Ensure teacher assignments have required relationships loaded
            $teacherAssignments->load(['teacher.user', 'subject', 'classroom']);

            // Get existing schedules
            $schedules = $this->getSchedulesByClassroomAndAcademicYear($classroomId, $academicYearId);

            return [
                'classroom' => $classroom,
                'academicYear' => $academicYear,
                'lessonHours' => $lessonHours,
                'teacherAssignments' => $teacherAssignments,
                'schedules' => $schedules->toArray(),
                'days' => config('constants.days'),
            ];
        } catch (\Exception $e) {
            \Log::error('Error getting schedule data: ' . $e->getMessage(), [
                'classroom_id' => $classroomId,
                'academic_year_id' => $academicYearId
            ]);
            throw new \Exception('Failed to retrieve schedule data: ' . $e->getMessage());
        }
    }

    /**
     * Validate schedule data for business rules
     */
    private function validateScheduleData(array $data): void
    {
        // Validate teacher assignment exists and is active
        try {
            $teacherAssignment = app(TeacherAssignmentService::class)->getTeacherAssignmentById($data['teacher_assignment_id']);
            if (!$teacherAssignment) {
                throw ClassScheduleException::validationError('Penugasan guru tidak ditemukan');
            }
        } catch (\Exception $e) {
            throw ClassScheduleException::validationError('Penugasan guru tidak valid: ' . $e->getMessage());
        }

        // Validate lesson hour exists
        try {
            $lessonHour = app(LessonHourService::class)->getLessonHourById($data['lesson_hour_id']);
            if (!$lessonHour) {
                throw ClassScheduleException::validationError('Jam pelajaran tidak ditemukan');
            }
        } catch (\Exception $e) {
            throw ClassScheduleException::validationError('Jam pelajaran tidak valid: ' . $e->getMessage());
        }

        // Validate day of week
        $validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        if (!in_array($data['day_of_week'], $validDays)) {
            throw ClassScheduleException::validationError('Hari tidak valid');
        }
    }

    /**
     * Validate schedule conflicts
     */
    private function validateScheduleConflicts(array $data, ?int $excludeId = null): void
    {
        // Check if there's already a schedule for this lesson hour and day
        $existingSchedule = $this->findExistingSchedule(
            $data['lesson_hour_id'],
            $data['day_of_week'],
            $excludeId
        );

        if ($existingSchedule) {
            throw ClassScheduleException::scheduleConflict('Jadwal untuk jam dan hari ini sudah ada');
        }

        // Get teacher ID from teacher assignment
        $teacherAssignment = app(TeacherAssignmentService::class)->getTeacherAssignmentById($data['teacher_assignment_id']);
        
        // Check if teacher has conflict at this time
        $hasConflict = $this->checkTeacherScheduleConflict(
            $teacherAssignment->teacher_id,
            $data['lesson_hour_id'],
            $data['day_of_week'],
            $excludeId
        );

        if ($hasConflict) {
            throw ClassScheduleException::teacherConflict('Guru sudah memiliki jadwal pada jam dan hari yang sama');
        }
    }
}
