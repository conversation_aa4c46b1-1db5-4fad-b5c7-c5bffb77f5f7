<?php

namespace App\Services;

use App\Contracts\Interfaces\LessonHourRepositoryInterface;
use App\Contracts\Services\LessonHourServiceInterface;
use App\Models\LessonHour;
use Illuminate\Database\Eloquent\Collection;

class LessonHourService implements LessonHourServiceInterface
{
    /**
     * The lesson hour repository instance.
     */
    protected LessonHourRepositoryInterface $lessonHourRepository;

    /**
     * Create a new LessonHourService instance.
     */
    public function __construct(LessonHourRepositoryInterface $lessonHourRepository)
    {
        $this->lessonHourRepository = $lessonHourRepository;
    }

    /**
     * Get all lesson hours with filtering.
     */
    public function getAllLessonHours(array $filters = []): Collection
    {
        return $this->lessonHourRepository->getAllLessonHours($filters);
    }

    /**
     * Get a lesson hour by ID.
     */
    public function getLessonHourById(int $id): LessonHour
    {
        return $this->lessonHourRepository->getLessonHourById($id);
    }

    /**
     * Create a new lesson hour.
     *
     * @throws \Exception
     */
    public function createLessonHour(array $data): LessonHour
    {
        return $this->lessonHourRepository->createLessonHour($data);
    }

    /**
     * Update an existing lesson hour.
     *
     * @throws \Exception
     */
    public function updateLessonHour(int $id, array $data): bool
    {
        return $this->lessonHourRepository->updateLessonHour($id, $data);
    }

    /**
     * Delete a lesson hour.
     *
     * @throws \Exception
     */
    public function deleteLessonHour(int $id): bool
    {
        return $this->lessonHourRepository->deleteLessonHour($id);
    }

    /**
     * Get lesson hours by classroom ID.
     */
    public function getLessonHoursByClassroomId(int $classroomId): Collection
    {
        return $this->lessonHourRepository->getLessonHoursByClassroomId($classroomId);
    }

    /**
     * Get total number of lesson hours.
     */
    public function getTotalLessonHours(): int
    {
        return $this->lessonHourRepository->count();
    }

    /**
     * Get lesson hours by shift ID.
     */
    public function getLessonHoursByShift(int $shiftId): Collection
    {
        return LessonHour::where('shift_id', $shiftId)
            ->orderBy('sequence')
            ->get();
    }
}
