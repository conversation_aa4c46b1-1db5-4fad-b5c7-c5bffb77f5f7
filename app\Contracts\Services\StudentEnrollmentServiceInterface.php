<?php

namespace App\Contracts\Services;

use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;

interface StudentEnrollmentServiceInterface
{
    /**
     * Enroll a student to a classroom for a specific academic year.
     *
     * @param int $studentId Student ID
     * @param int $classroomId Classroom ID
     * @param int $academicYearId Academic Year ID
     * @throws \App\Exceptions\BusinessLogicException If the student is already enrolled in this classroom for this academic year
     * @throws \App\Exceptions\NotFoundException If the student, classroom, or academic year is not found
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return bool
     */
    public function enrollStudentToClassroom(int $studentId, int $classroomId, int $academicYearId): bool;

    /**
     * Get all classrooms where a student is enrolled.
     *
     * @param int $studentId Student ID
     * @throws \App\Exceptions\NotFoundException If the student is not found
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getStudentClassrooms(int $studentId): Collection;

    /**
     * Get all students enrolled in a specific classroom for a specific academic year.
     *
     * @param int $classroomId Classroom ID
     * @param int $academicYearId Academic Year ID
     * @throws \App\Exceptions\NotFoundException If the classroom or academic year is not found
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getClassroomStudents(int $classroomId, int $academicYearId): Collection;
}
