<?php

namespace App\Contracts\Services;

use App\Models\LessonHour;
use Illuminate\Database\Eloquent\Collection;

interface LessonHourServiceInterface
{
    /**
     * Get all lesson hours with filtering.
     */
    public function getAllLessonHours(array $filters = []): Collection;

    /**
     * Get a lesson hour by ID.
     */
    public function getLessonHourById(int $id): LessonHour;

    /**
     * Create a new lesson hour.
     *
     * @throws \Exception
     */
    public function createLessonHour(array $data): LessonHour;

    /**
     * Update an existing lesson hour.
     *
     * @throws \Exception
     */
    public function updateLessonHour(int $id, array $data): bool;

    /**
     * Delete a lesson hour.
     *
     * @throws \Exception
     */
    public function deleteLessonHour(int $id): bool;

    /**
     * Get lesson hours by classroom ID.
     */
    public function getLessonHoursByClassroomId(int $classroomId): Collection;

    /**
     * Get total number of lesson hours.
     */
    public function getTotalLessonHours(): int;
}
