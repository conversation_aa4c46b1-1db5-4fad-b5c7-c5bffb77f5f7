<?php

namespace App\Contracts\Services;

use App\Models\Attendance;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface AttendanceServiceInterface
{
    /**
     * Get all attendances with optional filtering
     *
     * @return Collection|LengthAwarePaginator
     */
    public function getAllAttendances(array $filters = []);

    /**
     * Get attendance by ID
     */
    public function getAttendanceById(int $id): Attendance;

    /**
     * Record a new attendance
     */
    public function recordAttendance(array $data): Attendance;

    /**
     * Update an existing attendance
     */
    public function updateAttendance(int $id, array $data): bool;

    /**
     * Delete an attendance
     */
    public function deleteAttendance(int $id): bool;

    /**
     * Get class attendance for a specific date
     */
    public function getClassAttendance(int $classScheduleId, string $date): Collection;

    /**
     * Get student attendance with filters
     */
    public function getStudentAttendance(int $studentId, array $filters = []): Collection;

    /**
     * Get teacher attendance with filters
     */
    public function getTeacherAttendance(int $teacherId, array $filters = []): Collection;

    /**
     * Get attendance summary for a classroom in a specific month
     *
     * @param  string  $month  Format: YYYY-MM
     */
    public function getAttendanceSummary(int $classroomId, string $month): array;
}
