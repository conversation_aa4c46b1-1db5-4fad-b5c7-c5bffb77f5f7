<?php

namespace App\Contracts\Services;

use App\Models\Program;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;

interface ProgramServiceInterface
{
    /**
     * Get all programs.
     *
     * @return Collection
     */
    public function getAllPrograms();

    /**
     * Get all active programs.
     *
     * @return Collection
     */
    public function getAllActivePrograms();

    /**
     * Get a program by ID.
     *
     * @return Program
     */
    public function getProgramById(int $id);

    /**
     * Create a new program.
     *
     * @return Program
     */
    public function createProgram(array $data);

    /**
     * Update an existing program.
     *
     * @return Program
     */
    public function updateProgram(int $id, array $data);

    /**
     * Delete a program.
     *
     * @return bool
     */
    public function deleteProgram(int $id);

    /**
     * Get programs with subjects.
     *
     * @return Collection
     */
    public function getProgramsWithSubjects();

    /**
     * Get classrooms for a program.
     *
     * @return Collection
     */
    public function getProgramClassrooms(int $programId);

    /**
     * Get total number of programs.
     */
    public function getTotalPrograms(): int;

    /**
     * Get programs with classroom statistics.
     */
    public function getProgramsWithClassroomStats(): SupportCollection;

    /**
     * Get a list of all programs for API.
     *
     * @return Collection
     */
    public function getProgramsList();
}
