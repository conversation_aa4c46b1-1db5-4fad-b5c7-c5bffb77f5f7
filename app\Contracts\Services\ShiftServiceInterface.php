<?php

namespace App\Contracts\Services;

use App\Models\Shift;
use Illuminate\Database\Eloquent\Collection;

interface ShiftServiceInterface
{
    /**
     * Get all shifts
     */
    public function getAllShifts(array $filters = []): Collection;

    /**
     * Get a shift by ID
     */
    public function getShiftById(int $id): Shift;

    /**
     * Create a new shift
     */
    public function createShift(array $data): Shift;

    /**
     * Update a shift
     */
    public function updateShift(int $id, array $data): bool;

    /**
     * Delete a shift
     */
    public function deleteShift(int $id): bool;

    /**
     * Get all active shifts
     */
    public function getAllActiveShifts(): Collection;

    /**
     * Get total number of shifts
     */
    public function getTotalShifts(): int;
}
