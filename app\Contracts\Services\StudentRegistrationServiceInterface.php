<?php

namespace App\Contracts\Services;

use App\Models\Student;

interface StudentRegistrationServiceInterface
{
    /**
     * Register a new student with user account.
     *
     * @param array $data Student and user data
     * @throws \App\Exceptions\BusinessLogicException If validation fails
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return Student
     */
    public function registerStudent(array $data): Student;

    /**
     * Update an existing student and user data.
     *
     * @param int $id Student ID
     * @param array $data Student and user data
     * @throws \App\Exceptions\NotFoundException If the student is not found
     * @throws \App\Exceptions\BusinessLogicException If validation fails
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return bool
     */
    public function updateStudent(int $id, array $data): bool;

    /**
     * Delete a student and associated user account.
     *
     * @param int $id Student ID
     * @throws \App\Exceptions\NotFoundException If the student is not found
     * @throws \App\Exceptions\BusinessLogicException If the student cannot be deleted
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return bool
     */
    public function deleteStudent(int $id): bool;
}
