<?php

namespace App\Contracts\Services;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

interface StaffServiceInterface
{
    /**
     * Get all staff members with optional filters.
     */
    public function getAllStaff(array $filters = []): Collection;

    /**
     * Get all active staff members.
     */
    public function getAllActiveStaff(): Collection;

    /**
     * Get a staff member by ID.
     *
     * @throws \App\Exceptions\NotFoundException
     */
    public function getStaffById(int $id): User;

    /**
     * Create a new staff member with user account.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     */
    public function createStaff(array $data): User;

    /**
     * Update staff member data.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function updateStaff(int $id, array $data): bool;

    /**
     * Delete a staff member.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function deleteStaff(int $id): bool;

    /**
     * Get total count of staff members.
     */
    public function getTotalStaff(): int;
}
