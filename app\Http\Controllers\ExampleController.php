<?php

namespace App\Http\Controllers;

use App\Repositories\ExampleRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class ExampleController extends Controller
{
    protected $repository;

    public function __construct(ExampleRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Example of API endpoint with standardized error handling
     */
    public function apiExample(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users',
                'password' => 'required|min:8',
            ]);

            // Create user
            $user = $this->repository->createWithErrorHandling($validated);

            // Return success response
            return $this->successResponse($user, 'User created successfully');
        } catch (Throwable $e) {
            // Our custom exceptions will be caught by the global handler
            // This is just for any unexpected exceptions
            return $this->errorResponse(
                'An unexpected error occurred',
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Example of web endpoint with standardized error handling
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function webExample(Request $request)
    {
        try {
            // Validate request
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users',
                'password' => 'required|min:8',
            ]);

            // Create user
            $user = $this->repository->createWithErrorHandling($validated);

            // Return success response
            return $this->successRedirect('User created successfully', route('home'));
        } catch (\App\Exceptions\BusinessLogicException $e) {
            // Handle business logic exceptions
            return $this->errorRedirectBack($e->getMessage());
        } catch (Throwable $e) {
            // Handle unexpected exceptions
            return $this->errorRedirectBack('An unexpected error occurred');
        }
    }
}
