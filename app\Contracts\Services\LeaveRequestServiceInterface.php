<?php

namespace App\Contracts\Services;

use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\LeaveRequest;
use Illuminate\Database\Eloquent\Collection;

interface LeaveRequestServiceInterface
{
    /**
     * Get all leave requests with optional filtering.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Create a new leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function create(array $data): LeaveRequest;

    /**
     * Update a leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function delete(int $id): bool;

    /**
     * Find a leave request by ID.
     *
     * @throws NotFoundException
     */
    public function find(int $id): LeaveRequest;

    /**
     * Get all leave requests for admin listing (alias for getAll for backward compatibility).
     */
    public function getAllLeaveRequests(array $filters = []): Collection;

    /**
     * Create a leave request (alias for create for backward compatibility).
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function createLeaveRequest(array $data): LeaveRequest;

    /**
     * Update a leave request (alias for update for backward compatibility).
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function updateLeaveRequest(int $id, array $data): bool;

    /**
     * Delete a leave request (alias for delete for backward compatibility).
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function deleteLeaveRequest(int $id): bool;

    /**
     * Get a leave request by ID (alias for find for backward compatibility).
     *
     * @throws NotFoundException
     */
    public function getLeaveRequestById(int $id): LeaveRequest;
}
