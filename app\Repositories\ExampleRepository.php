<?php

namespace App\Repositories;

use App\Helpers\ErrorHandler;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class ExampleRepository
{
    /**
     * Example of standardized error handling in a repository method
     */
    public function createWithErrorHandling(array $data): User
    {
        DB::beginTransaction();
        try {
            // Validate data
            if (empty($data['name'])) {
                ErrorHandler::businessLogic('Name is required');
            }

            // Check if email already exists
            if (User::where('email', $data['email'])->exists()) {
                ErrorHandler::businessLogic('Email already exists');
            }

            // Create user
            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => bcrypt($data['password']),
            ]);

            DB::commit();

            return $user;
        } catch (Throwable $e) {
            DB::rollBack();

            // If it's already one of our custom exceptions, just rethrow it
            if ($e instanceof \App\Exceptions\BusinessLogicException) {
                throw $e;
            }

            // Otherwise, log and throw a database exception
            Log::error('Failed to create user: '.$e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            ErrorHandler::database('Failed to create user', $e);
        }
    }

    /**
     * Example of finding a resource with proper error handling
     */
    public function findWithErrorHandling(int $id): User
    {
        try {
            $user = User::find($id);

            if (! $user) {
                ErrorHandler::notFound("User with ID {$id} not found");
            }

            return $user;
        } catch (Throwable $e) {
            // If it's already one of our custom exceptions, just rethrow it
            if ($e instanceof \App\Exceptions\NotFoundException) {
                throw $e;
            }

            // Otherwise, log and throw a database exception
            Log::error('Failed to find user: '.$e->getMessage(), [
                'exception' => $e,
                'id' => $id,
            ]);

            ErrorHandler::database('Failed to find user', $e);
        }
    }
}
