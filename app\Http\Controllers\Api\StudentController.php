<?php

namespace App\Http\Controllers\Api;

use App\Contracts\Services\StudentServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Resources\StudentCollection;
use App\Http\Resources\StudentResource;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;

class StudentController extends Controller
{
    use ApiResponseTrait;

    protected $studentService;

    public function __construct(StudentServiceInterface $studentService)
    {
        $this->studentService = $studentService;
    }

    /**
     * Display a listing of the students.
     *
     * @return \App\Http\Resources\StudentCollection
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $students = $this->studentService->getAllStudents();

        if ($request->has('paginate') && $request->paginate === 'true') {
            $students = $students->paginate($perPage);
        }

        return new StudentCollection($students);
    }

    /**
     * Display the specified student.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse|\App\Http\Resources\StudentResource
     */
    public function show($id)
    {
        try {
            $student = $this->studentService->getStudentById($id);
            return new StudentResource($student);
        } catch (\Exception $e) {
            return $this->error('Student not found', 404);
        }
    }

    /**
     * Search for students based on criteria.
     *
     * @return \App\Http\Resources\StudentCollection
     */
    public function search(Request $request)
    {
        $filters = $request->only(['status', 'gender', 'search']);
        $students = $this->studentService->getAllStudents($filters);

        return new StudentCollection($students);
    }

    /**
     * Get student's classroom enrollments.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnrollments($id)
    {
        try {
            $student = $this->studentService->getStudentById($id);

            $enrollments = $student->classrooms()
                ->with('academicYear')
                ->get()
                ->map(function ($classroom) {
                    return [
                        'classroom_id' => $classroom->id,
                        'classroom_name' => $classroom->name,
                        'academic_year' => $classroom->academicYear->name,
                        'academic_year_id' => $classroom->academic_year_id,
                        'enrolled_at' => $classroom->pivot->created_at,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $enrollments,
                'message' => 'Student enrollments retrieved successfully',
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Student not found',
            ], 404);
        }
    }
}
