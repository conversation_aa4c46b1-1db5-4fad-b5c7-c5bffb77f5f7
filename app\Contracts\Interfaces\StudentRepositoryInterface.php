<?php

namespace App\Contracts\Interfaces;

use App\Contracts\Repositories\BaseRepositoryInterface;
use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;

interface StudentRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get all students with optional filters
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Get all active students
     */
    public function getAllActive(): Collection;

    /**
     * Find a student by ID
     */
    public function findById(int $id): Student;

    /**
     * Get a student by ID
     */
    public function getById(int $id): ?Student;

    /**
     * Create a new student
     */
    public function create(array $data): Student;

    /**
     * Update an existing student
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a student
     */
    public function delete(int $id): bool;

    /**
     * Get all students with relations
     */
    public function getAllWithRelations(array $relations, array $filters = []): Collection;

    /**
     * Get student by user ID
     */
    public function getByUserId(int $userId): ?Student;

    /**
     * Enroll student to a classroom
     */
    public function enrollToClassroom(int $studentId, int $classroomId, int $academicYearId): bool;

    /**
     * Get student's classrooms
     */
    public function getStudentClassrooms(int $studentId): Collection;

    public function getStudentPerformance(int $studentId);

    public function count(): int;

    /**
     * Create a new student with user account
     *
     * @throws \Exception
     */
    public function createStudentWithUser(array $data): Student;

    /**
     * Update student and user data
     *
     * @throws \Exception
     */
    public function updateStudentWithUser(int $id, array $data): bool;
}
