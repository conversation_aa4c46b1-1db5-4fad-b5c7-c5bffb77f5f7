<?php

namespace App\Services;

use App\Contracts\Interfaces\SubjectRepositoryInterface;
use App\Contracts\Services\SubjectServiceInterface;
use App\Models\Subject;
use Illuminate\Database\Eloquent\Collection;

class SubjectService implements SubjectServiceInterface
{
    /**
     * The subject repository instance.
     */
    protected SubjectRepositoryInterface $subjectRepository;

    /**
     * Create a new SubjectService instance.
     */
    public function __construct(SubjectRepositoryInterface $subjectRepository)
    {
        $this->subjectRepository = $subjectRepository;
    }

    /**
     * Get all subjects with filtering.
     */
    public function getAllSubjects(array $filters = []): Collection
    {
        return $this->subjectRepository->getAllSubjects($filters);
    }

    /**
     * Get a subject by ID.
     */
    public function getSubjectById(int $id): Subject
    {
        return $this->subjectRepository->getSubjectById($id);
    }

    /**
     * Create a new subject.
     *
     * @throws \Exception
     */
    public function createSubject(array $data): Subject
    {
        return $this->subjectRepository->createSubject($data);
    }

    /**
     * Update an existing subject.
     *
     * @throws \Exception
     */
    public function updateSubject(int $id, array $data): bool
    {
        return $this->subjectRepository->updateSubject($id, $data);
    }

    /**
     * Delete a subject.
     *
     * @throws \Exception
     */
    public function deleteSubject(int $id): bool
    {
        return $this->subjectRepository->deleteSubject($id);
    }

    /**
     * Get total number of subjects.
     */
    public function getTotalSubjects(): int
    {
        return $this->subjectRepository->count();
    }
}
