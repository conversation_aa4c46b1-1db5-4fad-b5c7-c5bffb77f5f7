<?php

namespace App\Contracts\Services;

use App\Models\Subject;
use Illuminate\Database\Eloquent\Collection;

interface SubjectServiceInterface
{
    /**
     * Get all subjects with filtering.
     */
    public function getAllSubjects(array $filters = []): Collection;

    /**
     * Get a subject by ID.
     */
    public function getSubjectById(int $id): Subject;

    /**
     * Create a new subject.
     *
     * @throws \Exception
     */
    public function createSubject(array $data): Subject;

    /**
     * Update an existing subject.
     *
     * @throws \Exception
     */
    public function updateSubject(int $id, array $data): bool;

    /**
     * Delete a subject.
     *
     * @throws \Exception
     */
    public function deleteSubject(int $id): bool;

    /**
     * Get total number of subjects.
     */
    public function getTotalSubjects(): int;
}
